# express-validator

[![npm version](https://img.shields.io/npm/v/express-validator.svg)](https://www.npmjs.com/package/express-validator)
[![Build status](https://github.com/express-validator/express-validator/actions/workflows/ci.yml/badge.svg)](https://github.com/express-validator/express-validator/actions/workflows/ci.yml)
[![Coverage Status](https://img.shields.io/coveralls/express-validator/express-validator.svg)](https://coveralls.io/github/express-validator/express-validator?branch=master)

An [express.js](https://github.com/visionmedia/express) middleware for
[validator](https://github.com/validatorjs/validator.js).

- [Installation](#installation)
- [Documentation](#documentation)
- [Changelog](#changelog)
- [License](#license)

## Installation

```
npm install express-validator
```

Also make sure that you have Node.js 14 or newer in order to use it.

## Documentation

Please refer to the documentation website on https://express-validator.github.io.

## Changelog

Check the [GitHub Releases page](https://github.com/express-validator/express-validator/releases).

## License

MIT License
