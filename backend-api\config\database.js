const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
  try {
    console.log('🔍 调试数据库配置:');
    console.log(`  Host: ${dbConfig.host}`);
    console.log(`  Port: ${dbConfig.port}`);
    console.log(`  User: ${dbConfig.user}`);
    console.log(`  Database: ${dbConfig.database}`);

    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    console.log(`📍 数据库地址: ${dbConfig.host}:${dbConfig.port}`);
    console.log(`🗄️  数据库名称: ${dbConfig.database}`);

    // 测试查询
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM stuinfosp');
    console.log(`👥 学生信息表记录数: ${rows[0].count}`);

    connection.release();
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('🔍 当前数据库配置:', dbConfig);
    throw error;
  }
}

// 执行查询的辅助函数
async function executeQuery(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
}

// 获取单条记录
async function findOne(sql, params = []) {
  const rows = await executeQuery(sql, params);
  return rows.length > 0 ? rows[0] : null;
}

// 获取多条记录
async function findMany(sql, params = []) {
  return await executeQuery(sql, params);
}

// 插入记录
async function insert(table, data) {
  const fields = Object.keys(data);
  const values = Object.values(data);
  const placeholders = fields.map(() => '?').join(', ');
  
  const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
  const result = await executeQuery(sql, values);
  return result;
}

// 更新记录
async function update(table, data, where, whereParams = []) {
  const fields = Object.keys(data);
  const values = Object.values(data);
  const setClause = fields.map(field => `${field} = ?`).join(', ');
  
  const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;
  const result = await executeQuery(sql, [...values, ...whereParams]);
  return result;
}

// 删除记录
async function remove(table, where, whereParams = []) {
  const sql = `DELETE FROM ${table} WHERE ${where}`;
  const result = await executeQuery(sql, whereParams);
  return result;
}

module.exports = {
  pool,
  executeQuery,
  findOne,
  findMany,
  insert,
  update,
  remove,
  testConnection
};
