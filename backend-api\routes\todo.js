const express = require('express');
const router = express.Router();

// 模拟待办事项数据
const mockTodos = [
  {
    id: 1,
    title: '完成学生注册',
    description: '完成新学期学生注册流程',
    status: 'pending',
    category: 'academic',
    priority: 'high',
    dueDate: '2024-12-31',
    createdAt: '2024-12-01',
    updatedAt: '2024-12-01'
  },
  {
    id: 2,
    title: '提交课程选择',
    description: '选择下学期的课程',
    status: 'pending',
    category: 'academic',
    priority: 'medium',
    dueDate: '2024-12-25',
    createdAt: '2024-12-01',
    updatedAt: '2024-12-01'
  },
  {
    id: 3,
    title: '缴纳学费',
    description: '完成下学期学费缴纳',
    status: 'completed',
    category: 'financial',
    priority: 'high',
    dueDate: '2024-12-20',
    createdAt: '2024-11-15',
    updatedAt: '2024-12-01'
  }
];

// 获取待办事项列表
router.get('/', (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      status = 'all',
      category = 'all',
      priority = 'all',
      search = ''
    } = req.query;

    let filteredTodos = [...mockTodos];

    // 状态过滤
    if (status !== 'all') {
      if (status === 'pending') {
        filteredTodos = filteredTodos.filter(todo => todo.status === 'pending');
      } else if (status === 'completed') {
        filteredTodos = filteredTodos.filter(todo => todo.status === 'completed');
      }
    }

    // 搜索过滤
    if (search) {
      filteredTodos = filteredTodos.filter(todo => 
        todo.title.toLowerCase().includes(search.toLowerCase()) ||
        todo.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // 分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + parseInt(pageSize);
    const paginatedTodos = filteredTodos.slice(startIndex, endIndex);

    res.json({
      code: 200,
      message: '获取成功',
      data: {
        list: paginatedTodos,
        total: filteredTodos.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        hasMore: endIndex < filteredTodos.length
      }
    });
  } catch (error) {
    console.error('获取待办事项失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取待办事项失败'
    });
  }
});

// 获取待办事项统计
router.get('/stats', (req, res) => {
  try {
    const total = mockTodos.length;
    const pending = mockTodos.filter(todo => todo.status === 'pending').length;
    const completed = mockTodos.filter(todo => todo.status === 'completed').length;

    res.json({
      code: 200,
      message: '获取成功',
      data: {
        total,
        pending,
        completed
      }
    });
  } catch (error) {
    console.error('获取统计失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取统计失败'
    });
  }
});

// 创建待办事项
router.post('/', (req, res) => {
  try {
    const { title, description, category, priority, dueDate } = req.body;

    if (!title) {
      return res.status(400).json({
        code: 400,
        message: '标题不能为空'
      });
    }

    const newTodo = {
      id: mockTodos.length + 1,
      title,
      description: description || '',
      status: 'pending',
      category: category || 'general',
      priority: priority || 'medium',
      dueDate: dueDate || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockTodos.push(newTodo);

    res.json({
      code: 200,
      message: '创建成功',
      data: newTodo
    });
  } catch (error) {
    console.error('创建待办事项失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建待办事项失败'
    });
  }
});

// 更新待办事项
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const todoIndex = mockTodos.findIndex(todo => todo.id === parseInt(id));

    if (todoIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '待办事项不存在'
      });
    }

    const updatedTodo = {
      ...mockTodos[todoIndex],
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    mockTodos[todoIndex] = updatedTodo;

    res.json({
      code: 200,
      message: '更新成功',
      data: updatedTodo
    });
  } catch (error) {
    console.error('更新待办事项失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新待办事项失败'
    });
  }
});

// 删除待办事项
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const todoIndex = mockTodos.findIndex(todo => todo.id === parseInt(id));

    if (todoIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '待办事项不存在'
      });
    }

    mockTodos.splice(todoIndex, 1);

    res.json({
      code: 200,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除待办事项失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除待办事项失败'
    });
  }
});

// 获取分类列表
router.get('/categories', (req, res) => {
  try {
    const categories = [
      { value: 'academic', label: '学术相关' },
      { value: 'administrative', label: '行政手续' },
      { value: 'accommodation', label: '住宿相关' },
      { value: 'financial', label: '财务相关' },
      { value: 'health', label: '健康体检' },
      { value: 'social', label: '社交活动' },
      { value: 'other', label: '其他' }
    ];

    res.json({
      code: 200,
      message: '获取成功',
      data: categories
    });
  } catch (error) {
    console.error('获取分类失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取分类失败'
    });
  }
});

module.exports = router;
