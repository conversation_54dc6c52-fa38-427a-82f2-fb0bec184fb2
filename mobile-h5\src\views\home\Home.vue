<template>
  <div class="home-page">
    <!-- 头部 -->
    <div class="home-header">
      <div class="user-greeting">
        <div class="avatar">
          <van-image
            :src="userStore.userAvatar"
            round
            width="40"
            height="40"
            fit="cover"
          >
            <template #error>
              <van-icon name="user-o" size="20" />
            </template>
          </van-image>
        </div>
        <div class="greeting-text">
          <div class="greeting">{{ greetingText }}</div>
          <div class="username">{{ userStore.userName }}</div>
        </div>
      </div>
      
      <div class="header-actions">
        <!-- 语言切换按钮 -->
        <van-button
          type="default"
          size="small"
          @click="toggleLanguage"
          class="language-btn"
        >
          {{ currentLanguage === 'zh-CN' ? 'EN' : '中文' }}
        </van-button>

        <!-- 退出登录按钮 -->
        <van-button
          type="default"
          size="small"
          @click="logout"
          class="logout-btn"
        >
          退出
        </van-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card" @click="goToTodos('all')">
          <div class="stat-number">{{ todoStats.total }}</div>
          <div class="stat-label">总待办</div>
        </div>
        <div class="stat-card" @click="goToTodos('pending')">
          <div class="stat-number">{{ todoStats.pending }}</div>
          <div class="stat-label">待处理</div>
        </div>
        <div class="stat-card" @click="goToTodos('completed')">
          <div class="stat-number">{{ todoStats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <!-- 移除已逾期统计卡片 -->
      </div>
      
      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-header">
          <span>完成进度</span>
          <span class="progress-text">{{ todoStats.completionRate }}%</span>
        </div>
        <van-progress
          :percentage="todoStats.completionRate"
          stroke-width="8"
          color="linear-gradient(to right, #1989fa, #07c160)"
        />
      </div>
    </div>

    <!-- 移除快捷操作区域 -->

    <!-- 移除紧急待办区域 -->

    <!-- 最近待办 -->
    <div class="recent-section">
      <div class="section-header">
        <h3 class="section-title">最近待办</h3>
        <van-button
          type="primary"
          size="small"
          plain
          @click="goToTodos('all')"
        >
          查看全部
        </van-button>
      </div>
      
      <div v-if="recentTodos.length > 0" class="todo-list">
        <div
          v-for="todo in recentTodos.slice(0, 5)"
          :key="todo.id"
          class="todo-item"
          @click="goToTodoDetail(todo.id)"
        >
          <div class="todo-checkbox">
            <van-checkbox
              :model-value="todo.completed"
              @click.stop="toggleTodo(todo)"
            />
          </div>
          <div class="todo-content">
            <div class="todo-title" :class="{ completed: todo.completed }">
              {{ todo.title }}
            </div>
            <div class="todo-meta">
              <span class="todo-category">{{ getCategoryText(todo.category) }}</span>
              <span class="todo-due-date">{{ formatDueDate(todo.dueDate) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <van-empty v-else description="暂无待办事项" />
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" @change="onTabChange">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="todo-list-o" to="/todo">待办</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useUserStore } from '../../stores/user'
import { useTodoStore } from '../../stores/todo'
import { useAppStore } from '../../stores/app'
import { utils } from '../../utils/global'
import { setLanguage, getCurrentLanguage } from '../../locales'

const router = useRouter()
const userStore = useUserStore()
const todoStore = useTodoStore()
const appStore = useAppStore()

const { todoStats, urgentTodos } = storeToRefs(todoStore)

// 响应式数据
const activeTab = ref(0)
const recentTodos = ref([])
const currentLanguage = ref(getCurrentLanguage())

// 计算属性
const greetingText = computed(() => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 方法
const goToTodos = (filter) => {
  router.push({
    path: '/todo',
    query: { filter }
  })
}

const goToTodoDetail = (id) => {
  router.push(`/todo/${id}`)
}

// 语言切换
const toggleLanguage = () => {
  const newLanguage = currentLanguage.value === 'zh-CN' ? 'en-US' : 'zh-CN'
  setLanguage(newLanguage)
  currentLanguage.value = newLanguage
}

// 退出登录
const logout = async () => {
  try {
    await userStore.logout()
    appStore.showToast('已退出登录', 'success')
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
    appStore.showToast('退出登录失败', 'error')
  }
}

const addTodo = () => {
  router.push('/todo/add')
}

const toggleTodo = async (todo) => {
  try {
    await todoStore.toggleTodoStatus(todo.id)
    appStore.showToast(todo.completed ? '待办已恢复' : '待办已完成', 'success')
  } catch (error) {
    console.error('切换待办状态失败:', error)
    appStore.showToast('操作失败', 'error')
  }
}

// 移除不需要的方法

const getCategoryText = (category) => {
  const categoryMap = {
    academic: '学术相关',
    administrative: '行政手续',
    accommodation: '住宿相关',
    financial: '财务相关',
    health: '健康体检',
    social: '社交活动',
    other: '其他'
  }
  return categoryMap[category] || '其他'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || '中'
}

const formatDueDate = (dueDate) => {
  if (!dueDate) return ''
  return utils.formatDate(dueDate, 'MM-DD')
}

const onTabChange = (index) => {
  // 标签页切换处理
}

const loadData = async () => {
  try {
    // 加载待办事项数据
    await todoStore.fetchTodos(true)
    
    // 获取最近的待办事项
    recentTodos.value = todoStore.todos.slice(0, 5)
    
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.home-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: 60px; // 为底部导航留空间
}

.home-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-lg);
  padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .user-greeting {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .avatar {
      .van-image {
        border: 2px solid rgba(255, 255, 255, 0.3);
      }
    }
    
    .greeting-text {
      .greeting {
        font-size: var(--font-size-sm);
        opacity: 0.9;
      }
      
      .username {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-sm);

    .language-btn, .logout-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      font-size: var(--font-size-sm);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .logout-btn {
      background: rgba(255, 87, 87, 0.2);
      border: 1px solid rgba(255, 87, 87, 0.3);

      &:hover {
        background: rgba(255, 87, 87, 0.3);
      }
    }
  }
}

.stats-section {
  margin: calc(-#{var(--spacing-lg)}) var(--spacing-lg) var(--spacing-lg);
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    .stat-card {
      text-align: center;
      padding: var(--spacing-md);
      border-radius: var(--border-radius-md);
      background: var(--bg-secondary);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
      }
      
      &.overdue {
        background: rgba(238, 10, 36, 0.1);
        
        .stat-number {
          color: var(--danger-color);
        }
      }
      
      .stat-number {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
        margin-bottom: var(--spacing-xs);
      }
      
      .stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
      }
    }
  }
  
  .progress-section {
    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-sm);
      
      .progress-text {
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
      }
    }
  }
}

// 移除快捷操作样式

.urgent-section,
.recent-section {
  margin: var(--spacing-lg);
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin: 0;
    }
  }
  
  .todo-list {
    .todo-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-md);
      border-radius: var(--border-radius-md);
      margin-bottom: var(--spacing-sm);
      background: var(--bg-secondary);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: var(--bg-tertiary);
      }
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .todo-checkbox {
        margin-right: var(--spacing-md);
      }
      
      .todo-content {
        flex: 1;
        
        .todo-title {
          font-size: var(--font-size-md);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
          margin-bottom: var(--spacing-xs);
          
          &.completed {
            text-decoration: line-through;
            color: var(--text-tertiary);
          }
        }
        
        .todo-meta {
          display: flex;
          gap: var(--spacing-md);
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          
          .todo-category {
            background: var(--primary-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: var(--font-size-xs);
          }
        }
      }
      
      .todo-priority {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
        
        &.low {
          background: rgba(7, 193, 96, 0.1);
          color: var(--success-color);
        }
        
        &.medium {
          background: rgba(255, 151, 106, 0.1);
          color: var(--warning-color);
        }
        
        &.high,
        &.urgent {
          background: rgba(238, 10, 36, 0.1);
          color: var(--danger-color);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .stats-section .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .quick-actions .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
